<route lang="json5">
{
  style: {
    navigationBarTitleText: '分时图数据测试',
    navigationBarBackgroundColor: '#F4A261',
    navigationBarTextStyle: 'white'
  },
}
</route>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import GoldChart from '@/components/GoldChart.vue'

const chartData = ref([])
const loading = ref(false)

// 模拟分时图数据（基于您提供的数据结构）
function generateTimeChartData() {
  const data = []
  const basePrice = 780
  const baseTime = new Date()
  
  // 生成24小时的分时数据（每小时一个数据点）
  for (let i = 0; i < 24; i++) {
    const time = new Date(baseTime.getTime() - (24 - i) * 60 * 60 * 1000)
    const timeStr = time.toISOString().slice(0, 19).replace('T', ' ')
    
    // 模拟价格波动
    const priceVariation = (Math.random() - 0.5) * 20
    const currentPrice = Math.max(0, basePrice + priceVariation)
    
    // 分时图数据结构
    const dataPoint = {
      _id: `test_${i}_${Date.now()}`,
      date_time: timeStr,
      buy: Math.floor(currentPrice - 2), // 买入价
      diffper: Number(((Math.random() - 0.5) * 2).toFixed(2)), // 涨跌幅
      high: Math.floor(currentPrice + Math.random() * 5), // 最高价
      low: Math.floor(currentPrice - Math.random() * 5), // 最低价
      new: Math.floor(currentPrice), // 当前价
      open: Math.floor(currentPrice + (Math.random() - 0.5) * 3), // 开盘价
      sell: Math.floor(currentPrice + 2), // 卖出价
      __v: 0
    }
    
    data.push(dataPoint)
  }
  
  return data
}

// 处理图表数据（与 gold/index.vue 中的函数保持一致）
function processChartData(data: any, timeFrame: string) {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return []
  }

  try {
    const processedData = data.map((item, index) => {
      if (!item) return null

      let processedItem
      
      if (timeFrame === 'time') {
        // 分时图数据处理
        processedItem = {
          time: item.date_time || item.time || new Date().toISOString(),
          open: Number(item.open) || Number(item.new) || 0,
          high: Number(item.high) || Number(item.new) || 0,
          low: Number(item.low) || Number(item.new) || 0,
          close: Number(item.new) || Number(item.close) || 0,
          volume: Number(item.volume) || 0,
          // 分时图特有字段
          buy: Number(item.buy) || 0,
          sell: Number(item.sell) || 0,
          diffper: Number(item.diffper) || 0,
        }
      } else {
        // K线图数据处理
        processedItem = {
          time: item.date_time || item.time || new Date().toISOString(),
          open: Number(item.open) || 0,
          high: Number(item.high) || 0,
          low: Number(item.low) || 0,
          close: Number(item.new) || Number(item.close) || 0,
          volume: Number(item.volume) || 0,
        }
      }

      // 验证数据合理性
      if (processedItem.high < processedItem.low) {
        if (timeFrame === 'time' && item.new) {
          processedItem.high = Number(item.new)
          processedItem.low = Number(item.new)
        } else {
          const temp = processedItem.high
          processedItem.high = processedItem.low
          processedItem.low = temp
        }
      }

      return processedItem
    }).filter(item => item !== null)

    return processedData
  } catch (error) {
    console.error('Error processing chart data:', error)
    return []
  }
}

// 加载测试数据
function loadTestData() {
  loading.value = true
  
  // 模拟API调用延迟
  setTimeout(() => {
    const rawData = generateTimeChartData()
    chartData.value = processChartData(rawData, 'time')
    loading.value = false
  }, 500)
}

// 显示原始数据结构
function showRawDataStructure() {
  const sampleData = generateTimeChartData().slice(0, 2)
  alert(`原始数据结构示例：\n${JSON.stringify(sampleData, null, 2)}`)
}

onMounted(() => {
  loadTestData()
})
</script>

<template>
  <view class="time-chart-test">
    <view class="header">
      <text class="title">分时图数据结构测试</text>
      <text class="subtitle">验证分时图数据的正确处理</text>
    </view>

    <!-- 控制按钮 -->
    <view class="controls">
      <button class="control-btn" @click="loadTestData">重新加载数据</button>
      <button class="control-btn" @click="showRawDataStructure">查看原始数据</button>
    </view>

    <!-- 数据信息 -->
    <view class="data-info">
      <view class="info-row">
        <text class="info-label">数据条数:</text>
        <text class="info-value">{{ chartData.length }}</text>
      </view>
      <view class="info-row">
        <text class="info-label">图表类型:</text>
        <text class="info-value">分时图</text>
      </view>
      <view class="info-row">
        <text class="info-label">加载状态:</text>
        <text class="info-value">{{ loading ? '加载中...' : '已完成' }}</text>
      </view>
    </view>

    <!-- 数据结构说明 -->
    <view class="data-structure">
      <view class="structure-title">原始数据结构:</view>
      <view class="structure-code">
        <text class="code-text">
{
  "_id": "68562648438129da99896d13",
  "date_time": "2025-06-21 00:00:00",
  "buy": 778,        // 买入价
  "diffper": 0.33,   // 涨跌幅
  "high": 780,       // 最高价
  "low": 780,        // 最低价
  "new": 780,        // 当前价
  "open": 780,       // 开盘价
  "sell": 784,       // 卖出价
  "__v": 0
}
        </text>
      </view>
    </view>

    <!-- 图表组件 -->
    <view class="chart-section">
      <GoldChart 
        :chart-data="chartData" 
        time-frame="time" 
        :loading="loading" 
      />
    </view>

    <!-- 处理后的数据示例 -->
    <view class="processed-data" v-if="chartData.length > 0">
      <view class="processed-title">处理后的数据示例:</view>
      <view class="processed-code">
        <text class="code-text">
{{ JSON.stringify(chartData.slice(0, 2), null, 2) }}
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.time-chart-test {
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(244, 162, 97, 0.05) 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;

  .title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 10rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #A0522D;
  }
}

.controls {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 30rpx;

  .control-btn {
    padding: 16rpx 32rpx;
    background: linear-gradient(135deg, #F4A261 0%, #E9C46A 100%);
    color: white;
    border: none;
    border-radius: 25rpx;
    font-size: 24rpx;
    font-weight: 500;
    box-shadow: 0 4rpx 12rpx rgba(244, 162, 97, 0.3);
  }
}

.data-info {
  background: rgba(244, 162, 97, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid rgba(244, 162, 97, 0.2);

  .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12rpx;

    .info-label {
      font-size: 24rpx;
      color: #A0522D;
      font-weight: 500;
    }

    .info-value {
      font-size: 24rpx;
      color: #8B4513;
      font-weight: 600;
    }
  }
}

.data-structure, .processed-data {
  background: rgba(233, 196, 106, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid rgba(233, 196, 106, 0.2);

  .structure-title, .processed-title {
    font-size: 26rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 16rpx;
  }

  .structure-code, .processed-code {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8rpx;
    padding: 16rpx;

    .code-text {
      font-family: 'Courier New', monospace;
      font-size: 20rpx;
      color: #2D5016;
      line-height: 1.4;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

.chart-section {
  margin-bottom: 30rpx;
}
</style>
