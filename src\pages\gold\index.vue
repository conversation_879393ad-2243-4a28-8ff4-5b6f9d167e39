<route lang="json5" type="home">
{
  style: {
    navigationBarTitleText: '金价实时行情',
    navigationBarBackgroundColor: '#F4A261',
    navigationBarTextStyle: 'white'
  },
}
</route>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { goldApi } from '@/api/gold'
import AIAnalysis from '@/components/AIAnalysis.vue'
import GoldChart from '@/components/GoldChart.vue'
import GoldPriceCard from '@/components/GoldPriceCard.vue'

// 响应式数据
const goldMarkets = ref([])
const chartData = ref([])
const chartLoading = ref(false)
const activeTab = ref('1day')
const lastUpdateTime = ref('')

// 时间选项卡
const timeTabs = [
  { label: '分时', value: 'time' },
  { label: '日K', value: '1day' },
  { label: '周K', value: '1week' },
  { label: '月K', value: '1month' },
]

// 格式化时间
function formatTime(date: Date): string {
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')
  return `${hours}:${minutes}:${seconds}`
}

// 处理图表数据
function processChartData(data: any, timeFrame: string) {
  if (!data) {
    console.warn('Chart data is null or undefined')
    return []
  }

  if (!Array.isArray(data)) {
    console.warn('Chart data is not an array:', typeof data, data)
    return []
  }

  if (data.length === 0) {
    console.warn('Chart data array is empty')
    return []
  }

  try {
    const processedData = data.map((item, index) => {
      if (!item) {
        console.warn(`Chart data item at index ${index} is null or undefined`)
        return null
      }

      const processedItem = {
        time: item.date_time || item.time || new Date().toISOString(),
        open: Number(item.open) || 0,
        high: Number(item.high) || 0,
        low: Number(item.low) || 0,
        close: Number(item.new) || Number(item.close) || 0,
        volume: Number(item.volume) || 0,
      }

      // 验证数据的合理性
      if (processedItem.high < processedItem.low) {
        console.warn(`Invalid price data at index ${index}: high (${processedItem.high}) < low (${processedItem.low})`)
        // 修正数据
        const temp = processedItem.high
        processedItem.high = processedItem.low
        processedItem.low = temp
      }

      if (processedItem.open < 0 || processedItem.close < 0 || processedItem.high < 0 || processedItem.low < 0) {
        console.warn(`Negative price values at index ${index}:`, processedItem)
      }

      return processedItem
    }).filter(item => item !== null) // 过滤掉无效数据

    return processedData
  }
  catch (error) {
    console.error('Error processing chart data:', error)
    return []
  }
}

// 加载金价数据
async function loadGoldPrices() {
  try {
    const response = await goldApi.getGoldPrices()
    goldMarkets.value = response
    lastUpdateTime.value = formatTime(new Date())
  }
  catch (error) {
    console.error('加载金价数据失败:', error)
    uni.showToast({
      title: '数据加载失败',
      icon: 'none',
    })
  }
}

// 加载图表数据
async function loadChartData() {
  chartLoading.value = true
  try {
    let response
    if (activeTab.value === 'time') {
      response = await goldApi.getTimeGoldData()
    }
    else {
      response = await goldApi.getDaysGoldData()
    }
    chartData.value = processChartData(response, activeTab.value)
  }
  catch (error) {
    console.error('加载图表数据失败:', error)
    uni.showToast({
      title: '图表数据加载失败',
      icon: 'none',
    })
  }
  finally {
    chartLoading.value = false
  }
}

// 切换时间框架
function switchTimeFrame(timeFrame: string) {
  activeTab.value = timeFrame
  loadChartData()
}

// 刷新数据
function refreshData() {
  loadGoldPrices()
  // loadChartData()
}

// 页面加载时初始化数据
onMounted(() => {
  loadGoldPrices()
  loadChartData()

  // 设置定时刷新（每30秒）
  setInterval(() => {
    refreshData()
  }, 30000)
})
</script>

<template>
  <view class="gold-info-container">
    <!-- 顶部实时价格展示 -->
    <view class="price-header">
      <GoldPriceCard v-for="market in goldMarkets" :key="market.type" :market-data="market" />
    </view>

    <!-- K线图区域 -->
    <view class="chart-section">
      <view class="chart-header">
        <text class="chart-title">
          金价走势图
        </text>
        <view class="time-tabs">
          <text
            v-for="tab in timeTabs" :key="tab.value"
            class="tab-item" :class="[{ active: activeTab === tab.value }]" @click="switchTimeFrame(tab.value)"
          >
            {{ tab.label }}
          </text>
        </view>
      </view>
      <view class="chart-container">
        <GoldChart :chart-data="chartData" :time-frame="activeTab" :loading="chartLoading" />
      </view>
    </view>

    <!-- AI分析区域 -->
    <view class="ai-analysis-section">
      <view class="analysis-header">
        <text class="analysis-title">
          AI智能分析
        </text>
        <text class="update-time">
          更新时间: {{ lastUpdateTime }}
        </text>
      </view>
      <view class="analysis-content">
        <AIAnalysis :chart-data="chartData" :market-data="goldMarkets" />
      </view>
    </view>
  </view>
</template>

<style scoped>
.gold-info-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f4a261 0%, #e76f51 50%, #e9c46a 100%);
  padding: 20rpx;
  position: relative;
  overflow: hidden;
}

.gold-info-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 40%),
    radial-gradient(circle at 80% 70%, rgba(231, 111, 81, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(233, 196, 106, 0.1) 0%, transparent 60%);
  pointer-events: none;
  z-index: 0;
}

.price-header {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

/* 响应式设计 - 小屏幕时保持三列但调整间距 */
@media (max-width: 750px) {
  .price-header {
    gap: 12rpx;
  }
}

.chart-section {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(20px);
  border: 2rpx solid rgba(244, 162, 97, 0.3);
  box-shadow:
    0 12rpx 40rpx rgba(231, 111, 81, 0.15),
    0 4rpx 12rpx rgba(244, 162, 97, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 1;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.chart-title {
  font-size: 30rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #e76f51 0%, #f4a261 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #e76f51;
}

.time-tabs {
  display: flex;
  gap: 12rpx;
}

.tab-item {
  padding: 12rpx 24rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  color: #8d5524;
  background: linear-gradient(145deg, rgba(244, 162, 97, 0.1) 0%, rgba(233, 196, 106, 0.1) 100%);
  border: 1rpx solid rgba(244, 162, 97, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
}

.tab-item.active {
  color: #ffffff;
  background: linear-gradient(135deg, #e76f51 0%, #f4a261 100%);
  border: 1rpx solid #e76f51;
  box-shadow: 0 4rpx 12rpx rgba(231, 111, 81, 0.3);
  transform: translateY(-2rpx);
}

.chart-container {
  height: 800rpx;
  border-radius: 18rpx;
  overflow: hidden;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.5) 0%, rgba(244, 162, 97, 0.05) 100%);
  border: 1rpx solid rgba(244, 162, 97, 0.2);
}

.ai-analysis-section {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  border-radius: 24rpx;
  padding: 30rpx;
  backdrop-filter: blur(20px);
  border: 2rpx solid rgba(244, 162, 97, 0.3);
  box-shadow:
    0 12rpx 40rpx rgba(231, 111, 81, 0.15),
    0 4rpx 12rpx rgba(244, 162, 97, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 1;
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.analysis-title {
  font-size: 30rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #e76f51 0%, #f4a261 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #e76f51;
}

.update-time {
  font-size: 22rpx;
  color: #8d5524;
  background: linear-gradient(135deg, rgba(244, 162, 97, 0.1) 0%, rgba(233, 196, 106, 0.1) 100%);
  padding: 6rpx 12rpx;
  border-radius: 18rpx;
  border: 1rpx solid rgba(244, 162, 97, 0.2);
}

.analysis-content {
  min-height: 200rpx;
}
</style>
