<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue'

interface ChartDataPoint {
  time: string
  open: number
  high: number
  low: number
  close: number
  volume?: number
}

interface Props {
  chartData: ChartDataPoint[]
  timeFrame: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const selectedPoint = ref<ChartDataPoint | null>(null)
const canvasId = ref(`goldChart_${Date.now()}`)

// 图表类型
const chartType = computed(() => {
  return props.timeFrame === 'time' ? 'line' : 'candle'
})

// uCharts 配置选项 - 美化的金色主题配置
const chartOptions = computed(() => {
  const isTimeChart = props.timeFrame === 'time'

  return {
    // 金色主题色彩搭配
    color: ['#D4AF37', '#B8860B', '#DAA520', '#CD853F', '#F4A460', '#DEB887', '#D2691E', '#A0522D', '#8B4513'],
    padding: [20, 20, 10, 20],
    enableScroll: true,
    enableMarkLine: false,
    dataLabel: false,
    dataPointShape: false,
    background: 'transparent',
    pixelRatio: 2,
    animation: true,
    xAxis: {
      disableGrid: false,
      type: 'category',
      gridType: 'dash',
      dashLength: 3,
      gridColor: 'rgba(212, 175, 55, 0.15)',
      fontColor: '#8B4513',
      fontSize: 10,
      rotateLabel: false,
      itemCount: isTimeChart ? 6 : 30,
      scrollShow: true,
      scrollAlign: 'right',
      boundaryGap: true,
      labelCount: 5,
    },
    yAxis: {
      gridType: 'dash',
      dashLength: 3,
      gridColor: 'rgba(212, 175, 55, 0.15)',
      fontColor: '#8B4513',
      fontSize: 10,
      format: (val: number) => val.toFixed(2),
    },
    legend: {
      show: false,
    },
    extra: {
      candle: {
        color: {
          // 中国股市传统：红涨绿跌
          upLine: '#DC143C', // 深红色 - 上涨
          upFill: '#DC143C', // 深红色 - 上涨填充
          downLine: '#228B22', // 森林绿 - 下跌
          downFill: '#228B22', // 森林绿 - 下跌填充
        },
        border: 1,
        width: 0.8,
        average: {
          show: true,
          name: ['MA5', 'MA10', 'MA20'],
          day: [5, 10, 20],
          color: [
            '#D4AF37', // 金色 - MA5
            '#B8860B', // 暗金色 - MA10
            '#CD853F', // 秘鲁色 - MA20
          ],
        },
      },
      line: {
        type: 'curve',
        width: 3,
        activeType: 'hollow',
        color: '#D4AF37',
        activeColor: '#B8860B',
      },
      tooltip: {
        showCategory: true,
        bgColor: 'rgba(255, 255, 255, 0.95)',
        bgOpacity: 0.95,
        borderColor: '#D4AF37',
        borderWidth: 1,
        borderRadius: 8,
        fontColor: '#8B4513',
        fontSize: 12,
      },
    },
  }
})

// 转换数据为 uCharts 格式
const uChartsData = computed(() => {
  if (!props.chartData || props.chartData.length === 0) {
    return {
      categories: [],
      series: [],
    }
  }

  const categories = props.chartData.map(item => formatTime(item.time))

  if (props.timeFrame === 'time') {
    // 分时图 - 使用线图
    return {
      categories,
      series: [{
        name: '价格',
        data: props.chartData.map(item => item.close),
        color: '#ffc107',
      }],
    }
  }
  else {
    // K线图 - 使用蜡烛图
    return {
      categories,
      series: [{
        name: 'K线',
        data: props.chartData.map(item => [item.open, item.close, item.low, item.high]),
      }],
    }
  }
})

// 计算最新价格
function getLatestPrice() {
  if (!props.chartData || props.chartData.length === 0)
    return '--'
  const latest = props.chartData[props.chartData.length - 1]
  return latest.close.toFixed(2)
}

// 计算价格变化
function getChangeValue() {
  if (!props.chartData || props.chartData.length < 2)
    return '--'
  const latest = props.chartData[props.chartData.length - 1]
  const previous = props.chartData[props.chartData.length - 2]
  const change = latest.close - previous.close
  const sign = change > 0 ? '+' : ''
  return `${sign}${change.toFixed(2)}`
}

// 计算涨跌幅
function getChangePercent() {
  if (!props.chartData || props.chartData.length < 2)
    return '--'
  const latest = props.chartData[props.chartData.length - 1]
  const previous = props.chartData[props.chartData.length - 2]
  const change = latest.close - previous.close
  const percent = (change / previous.close) * 100
  const sign = percent > 0 ? '+' : ''
  return `${sign}${percent.toFixed(2)}%`
}

// 获取最新价格样式类
function getLatestPriceClass() {
  if (!props.chartData || props.chartData.length < 2)
    return 'neutral'
  const latest = props.chartData[props.chartData.length - 1]
  const previous = props.chartData[props.chartData.length - 2]
  const change = latest.close - previous.close
  if (change > 0)
    return 'up'
  if (change < 0)
    return 'down'
  return 'neutral'
}

// 获取变化样式类
function getChangeClass() {
  return getLatestPriceClass()
}

// 获取价格变化样式类
function getPriceChangeClass(point: ChartDataPoint) {
  const change = point.close - point.open
  if (change > 0)
    return 'up'
  if (change < 0)
    return 'down'
  return 'neutral'
}

// 格式化时间
function formatTime(timeStr: string) {
  try {
    const date = new Date(timeStr)
    if (props.timeFrame === 'time') {
      // 分时图显示时:分
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    }
    else {
      // K线图显示月-日
      return `${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
    }
  }
  catch (error) {
    return timeStr
  }
}

// 格式化成交量
function formatVolume(volume: number) {
  if (volume >= 10000) {
    return `${(volume / 10000).toFixed(1)}万`
  }
  return volume.toString()
}

// uCharts 事件处理
function onChartTouch(index: number) {
  if (props.chartData && props.chartData[index]) {
    selectedPoint.value = props.chartData[index]
  }
}

// 监听数据变化
watch(() => props.chartData, () => {
  if (props.chartData && props.chartData.length > 0) {
    selectedPoint.value = props.chartData[props.chartData.length - 1]
  }
}, { immediate: true })

// 监听时间框架变化
watch(() => props.timeFrame, () => {
  // 时间框架变化时重新设置选中点
  if (props.chartData && props.chartData.length > 0) {
    selectedPoint.value = props.chartData[props.chartData.length - 1]
  }
})

onMounted(() => {
  // 初始化选中点
  if (props.chartData && props.chartData.length > 0) {
    selectedPoint.value = props.chartData[props.chartData.length - 1]
  }
})
</script>

<template>
  <view class="chart-container">
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner" />
      <text class="loading-text">
        加载中...
      </text>
    </view>

    <view v-else-if="!chartData || chartData.length === 0" class="empty-container">
      <text class="empty-text">
        暂无数据
      </text>
    </view>

    <view v-else class="chart-content">
      <!-- 价格信息显示 -->
      <view v-if="selectedPoint" class="price-info">
        <view class="price-info-item">
          <text class="price-label">
            时间:
          </text>
          <text class="price-value">
            {{ formatTime(selectedPoint.time) }}
          </text>
        </view>
        <view class="price-info-item">
          <text class="price-label">
            开盘:
          </text>
          <text class="price-value">
            {{ selectedPoint.open.toFixed(2) }}
          </text>
        </view>
        <view class="price-info-item">
          <text class="price-label">
            收盘:
          </text>
          <text class="price-value" :class="getPriceChangeClass(selectedPoint)">
            {{ selectedPoint.close.toFixed(2) }}
          </text>
        </view>
        <view class="price-info-item">
          <text class="price-label">
            最高:
          </text>
          <text class="price-value high">
            {{ selectedPoint.high.toFixed(2) }}
          </text>
        </view>
        <view class="price-info-item">
          <text class="price-label">
            最低:
          </text>
          <text class="price-value low">
            {{ selectedPoint.low.toFixed(2) }}
          </text>
        </view>
        <view v-if="timeFrame !== 'time' && selectedPoint.volume" class="price-info-item">
          <text class="price-label">
            成交量:
          </text>
          <text class="price-value">
            {{ formatVolume(selectedPoint.volume) }}
          </text>
        </view>
      </view>

      <!-- uCharts 图表组件 -->
      <view class="chart-wrapper">
        <qiun-data-charts
          :type="chartType"
          :opts="chartOptions"
          :chart-data="uChartsData"
          :canvas2d="true"
          :canvas-id="canvasId"
          @get-index="onChartTouch"
        />
      </view>

      <!-- 图表统计信息 -->
      <view class="chart-stats">
        <view class="stat-item">
          <text class="stat-label">
            最新价
          </text>
          <text class="stat-value" :class="getLatestPriceClass()">
            {{ getLatestPrice() }}
          </text>
        </view>
        <view class="stat-item">
          <text class="stat-label">
            涨跌
          </text>
          <text class="stat-value" :class="getChangeClass()">
            {{ getChangeValue() }}
          </text>
        </view>
        <view class="stat-item">
          <text class="stat-label">
            涨跌幅
          </text>
          <text class="stat-value" :class="getChangeClass()">
            {{ getChangePercent() }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.chart-container {
  width: 100%;
  height: 800rpx;
  position: relative;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 18rpx;
  overflow: hidden;
  border: 2rpx solid rgba(244, 162, 97, 0.2);
  backdrop-filter: blur(10px);
  box-shadow:
    0 4rpx 16rpx rgba(231, 111, 81, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.7);
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(244, 162, 97, 0.3);
  border-top: 4rpx solid #f4a261;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 20rpx;
  color: #8d5524;
  font-size: 24rpx;
  font-weight: 500;
}

.chart-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.empty-text {
  color: #8d5524;
  font-size: 26rpx;
  font-weight: 500;
}

.price-info {
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(244, 162, 97, 0.08) 0%, rgba(233, 196, 106, 0.05) 100%);
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  border-bottom: 2rpx solid rgba(244, 162, 97, 0.2);
}

.price-info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 100rpx;
}

.price-label {
  font-size: 20rpx;
  color: #8d5524;
  font-weight: 500;
}

.price-value {
  font-size: 22rpx;
  color: #a0522d;
  font-weight: 600;
}

.chart-wrapper {
  flex: 1;
  width: 100%;
  height: 400rpx;
  position: relative;
}

.chart-stats {
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(244, 162, 97, 0.08) 0%, rgba(233, 196, 106, 0.05) 100%);
  display: flex;
  justify-content: space-around;
  border-top: 2rpx solid rgba(244, 162, 97, 0.2);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 20rpx;
  color: #8d5524;
  display: block;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.stat-value {
  font-size: 24rpx;
  color: #a0522d;
  font-weight: 600;
}

/* 价格变化颜色 - 中国股市传统：红涨绿跌 */
.up {
  color: #dc143c !important; /* 深红色表示上涨 */
}

.down {
  color: #228b22 !important; /* 森林绿表示下跌 */
}

.neutral {
  color: #d4af37 !important; /* 金色表示持平 */
}

.high {
  color: #dc143c !important; /* 最高价用红色 */
}

.low {
  color: #228b22 !important; /* 最低价用绿色 */
}
</style>
