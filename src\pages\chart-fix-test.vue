<route lang="json5">
{
  style: {
    navigationBarTitleText: '图表修复测试',
    navigationBarBackgroundColor: '#F4A261',
    navigationBarTextStyle: 'white'
  },
}
</route>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import GoldChart from '@/components/GoldChart.vue'

const chartData = ref([])
const activeTab = ref('1day')
const loading = ref(false)

// 生成安全的测试数据
function generateSafeTestData() {
  const data = []
  const basePrice = 2000
  
  for (let i = 0; i < 20; i++) {
    const time = new Date(Date.now() - (20 - i) * 86400000).toISOString()
    const open = basePrice + (Math.random() - 0.5) * 50
    const close = open + (Math.random() - 0.5) * 30
    const high = Math.max(open, close) + Math.random() * 20
    const low = Math.min(open, close) - Math.random() * 20
    
    data.push({
      time,
      open: Number(open.toFixed(2)),
      high: Number(high.toFixed(2)),
      low: Number(low.toFixed(2)),
      close: Number(close.toFixed(2)),
      volume: Math.floor(Math.random() * 100000),
    })
  }
  
  return data
}

// 测试不同的数据情况
function testEmptyData() {
  chartData.value = []
  console.log('Testing with empty data')
}

function testNormalData() {
  chartData.value = generateSafeTestData()
  console.log('Testing with normal data:', chartData.value.length, 'items')
}

function testSingleData() {
  const singleItem = {
    time: new Date().toISOString(),
    open: 2000,
    high: 2050,
    low: 1950,
    close: 2020,
    volume: 50000,
  }
  chartData.value = [singleItem]
  console.log('Testing with single data item')
}

function testInvalidData() {
  // 测试包含无效数据的情况
  const invalidData = [
    {
      time: new Date().toISOString(),
      open: 2000,
      high: 2050,
      low: 1950,
      close: 2020,
      volume: 50000,
    },
    null, // 无效数据
    {
      time: new Date().toISOString(),
      open: 'invalid', // 无效价格
      high: 2100,
      low: 1900,
      close: 2050,
      volume: 60000,
    },
  ]
  chartData.value = invalidData
  console.log('Testing with invalid data')
}

onMounted(() => {
  testNormalData()
})
</script>

<template>
  <view class="chart-fix-test">
    <view class="header">
      <text class="title">图表点击错误修复测试</text>
      <text class="subtitle">测试不同数据情况下的图表交互</text>
    </view>

    <!-- 测试按钮 -->
    <view class="test-buttons">
      <button class="test-btn" @click="testNormalData">正常数据</button>
      <button class="test-btn" @click="testEmptyData">空数据</button>
      <button class="test-btn" @click="testSingleData">单条数据</button>
      <button class="test-btn" @click="testInvalidData">无效数据</button>
    </view>

    <!-- 数据信息 -->
    <view class="data-info">
      <text class="info-text">数据条数: {{ chartData.length }}</text>
      <text class="info-text">时间框架: {{ activeTab }}</text>
    </view>

    <!-- 图表组件 -->
    <view class="chart-section">
      <GoldChart 
        :chart-data="chartData" 
        :time-frame="activeTab" 
        :loading="loading" 
      />
    </view>

    <!-- 说明 -->
    <view class="instructions">
      <view class="instruction-title">测试说明:</view>
      <view class="instruction-item">
        <text class="instruction-text">1. 点击不同的测试按钮切换数据</text>
      </view>
      <view class="instruction-item">
        <text class="instruction-text">2. 尝试点击图表区域</text>
      </view>
      <view class="instruction-item">
        <text class="instruction-text">3. 观察控制台是否有错误信息</text>
      </view>
      <view class="instruction-item">
        <text class="instruction-text">4. 检查图表是否正常显示和交互</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.chart-fix-test {
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(244, 162, 97, 0.05) 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;

  .title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 10rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #A0522D;
  }
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  justify-content: center;
  margin-bottom: 30rpx;

  .test-btn {
    padding: 20rpx 30rpx;
    background: linear-gradient(135deg, #F4A261 0%, #E9C46A 100%);
    color: white;
    border: none;
    border-radius: 25rpx;
    font-size: 24rpx;
    font-weight: 500;
    box-shadow: 0 4rpx 12rpx rgba(244, 162, 97, 0.3);
    
    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 2rpx 8rpx rgba(244, 162, 97, 0.3);
    }
  }
}

.data-info {
  background: rgba(244, 162, 97, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  border: 2rpx solid rgba(244, 162, 97, 0.2);

  .info-text {
    font-size: 24rpx;
    color: #8B4513;
    font-weight: 500;
  }
}

.chart-section {
  margin-bottom: 30rpx;
}

.instructions {
  background: rgba(233, 196, 106, 0.1);
  border-radius: 12rpx;
  padding: 24rpx;
  border: 2rpx solid rgba(233, 196, 106, 0.2);

  .instruction-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 16rpx;
  }

  .instruction-item {
    margin-bottom: 12rpx;

    .instruction-text {
      font-size: 24rpx;
      color: #A0522D;
      line-height: 1.5;
    }
  }
}
</style>
