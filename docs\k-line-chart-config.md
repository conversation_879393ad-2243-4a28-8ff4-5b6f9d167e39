# K线图美化配置说明

## 概述

基于官方 uCharts K线图配置，结合项目的金色主题和中国股市颜色传统，对K线图进行了全面美化。

## 主要改进

### 1. 颜色主题
- **主色调**: 金色系配色方案，温暖优雅
- **涨跌颜色**: 采用中国股市传统 - 红涨绿跌
- **配色方案**:
  ```javascript
  color: ["#D4AF37", "#B8860B", "#DAA520", "#CD853F", "#F4A460", "#DEB887", "#D2691E", "#A0522D", "#8B4513"]
  ```

### 2. K线颜色配置
```javascript
candle: {
  color: {
    upLine: "#DC143C",      // 深红色 - 上涨
    upFill: "#DC143C",      // 深红色 - 上涨填充
    downLine: "#228B22",    // 森林绿 - 下跌
    downFill: "#228B22"     // 森林绿 - 下跌填充
  }
}
```

### 3. 移动平均线
- **MA5**: #D4AF37 (金色)
- **MA10**: #B8860B (暗金色)  
- **MA20**: #CD853F (秘鲁色)

### 4. 网格线优化
- 使用虚线样式 (`gridType: "dash"`)
- 金色半透明网格 (`gridColor: "rgba(212, 175, 55, 0.15)"`)
- 更细腻的虚线间距 (`dashLength: 3`)

### 5. 滚动条美化
- 金色滚动条 (`scrollColor: "#D4AF37"`)
- 半透明背景 (`scrollBackgroundColor: "rgba(244, 164, 96, 0.1)"`)
- 右对齐显示 (`scrollAlign: "right"`)

### 6. 提示框样式
```javascript
tooltip: {
  showCategory: true,
  bgColor: "rgba(255, 255, 255, 0.95)",
  bgOpacity: 0.95,
  borderColor: "#D4AF37",
  borderWidth: 1,
  borderRadius: 8,
  fontColor: "#8B4513",
  fontSize: 12
}
```

## 完整配置示例

```javascript
opts: {
  rotate: false,
  rotateLock: false,
  color: ["#D4AF37", "#B8860B", "#DAA520", "#CD853F", "#F4A460", "#DEB887", "#D2691E", "#A0522D", "#8B4513"],
  padding: [20, 20, 10, 20],
  dataLabel: false,
  enableScroll: true,
  enableMarkLine: false,
  animation: true,
  pixelRatio: 2,
  background: 'transparent',
  legend: {
    show: false
  },
  xAxis: {
    labelCount: 5,
    itemCount: 30,
    disableGrid: false,
    gridColor: "rgba(212, 175, 55, 0.15)",
    gridType: "dash",
    dashLength: 3,
    scrollShow: true,
    scrollAlign: "right",
    scrollColor: "#D4AF37",
    scrollBackgroundColor: "rgba(244, 164, 96, 0.1)",
    fontColor: "#8B4513",
    fontSize: 10,
    rotateLabel: false,
    boundaryGap: true
  },
  yAxis: {
    gridColor: "rgba(212, 175, 55, 0.15)",
    gridType: "dash",
    dashLength: 3,
    fontColor: "#8B4513",
    fontSize: 10,
    format: (val) => val.toFixed(2)
  },
  extra: {
    candle: {
      color: {
        upLine: "#DC143C",
        upFill: "#DC143C",
        downLine: "#228B22",
        downFill: "#228B22"
      },
      average: {
        show: true,
        name: ["MA5", "MA10", "MA20"],
        day: [5, 10, 20],
        color: ["#D4AF37", "#B8860B", "#CD853F"]
      }
    },
    tooltip: {
      showCategory: true,
      bgColor: "rgba(255, 255, 255, 0.95)",
      bgOpacity: 0.95,
      borderColor: "#D4AF37",
      borderWidth: 1,
      borderRadius: 8,
      fontColor: "#8B4513",
      fontSize: 12
    }
  }
}
```

## 使用方法

### 1. 直接使用美化组件
```vue
<template>
  <BeautifulKLineChart />
</template>

<script>
import BeautifulKLineChart from '@/components/BeautifulKLineChart.vue'
export default {
  components: { BeautifulKLineChart }
}
</script>
```

### 2. 自定义配置
```vue
<template>
  <qiun-data-charts 
    type="candle"
    :opts="customOpts"
    :chartData="chartData"
  />
</template>
```

## 文件说明

- `BeautifulKLineChart.vue` - 完整美化版K线图组件
- `SimpleKLineChart.vue` - 简化版美化K线图
- `config-ucharts.js` - 全局配置已更新为美化版本
- `chart-demo.vue` - 演示页面，展示美化效果

## 颜色对照表

| 用途 | 颜色值 | 说明 |
|------|--------|------|
| 上涨 | #DC143C | 深红色 |
| 下跌 | #228B22 | 森林绿 |
| 主金色 | #D4AF37 | MA5、滚动条 |
| 暗金色 | #B8860B | MA10 |
| 秘鲁色 | #CD853F | MA20 |
| 文字色 | #8B4513 | 坐标轴文字 |
| 网格线 | rgba(212, 175, 55, 0.15) | 半透明金色 |

## 注意事项

1. 确保项目中已安装 `qiun-data-charts` 组件
2. 配置中的颜色值遵循中国股市传统（红涨绿跌）
3. 网格线使用半透明效果，确保图表清晰度
4. 移动平均线颜色与主题保持一致
5. 提示框样式与整体设计风格统一
