/**
 * 图表调试工具
 * 用于诊断和解决图表交互问题
 */

export interface ChartDataPoint {
  time: string
  open: number
  high: number
  low: number
  close: number
  volume?: number
}

/**
 * 验证图表数据的完整性
 */
export function validateChartData(data: any[]): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  if (!Array.isArray(data)) {
    errors.push('Data is not an array')
    return { isValid: false, errors, warnings }
  }

  if (data.length === 0) {
    warnings.push('Data array is empty')
    return { isValid: true, errors, warnings }
  }

  data.forEach((item, index) => {
    if (!item) {
      errors.push(`Item at index ${index} is null or undefined`)
      return
    }

    // 检查必需字段
    if (!item.time) {
      errors.push(`Item at index ${index} missing time field`)
    }

    if (typeof item.open !== 'number' || isNaN(item.open)) {
      errors.push(`Item at index ${index} has invalid open price: ${item.open}`)
    }

    if (typeof item.high !== 'number' || isNaN(item.high)) {
      errors.push(`Item at index ${index} has invalid high price: ${item.high}`)
    }

    if (typeof item.low !== 'number' || isNaN(item.low)) {
      errors.push(`Item at index ${index} has invalid low price: ${item.low}`)
    }

    if (typeof item.close !== 'number' || isNaN(item.close)) {
      errors.push(`Item at index ${index} has invalid close price: ${item.close}`)
    }

    // 检查价格逻辑
    if (item.high < item.low) {
      warnings.push(`Item at index ${index}: high price (${item.high}) is less than low price (${item.low})`)
    }

    if (item.open < 0 || item.close < 0 || item.high < 0 || item.low < 0) {
      warnings.push(`Item at index ${index} has negative price values`)
    }
  })

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 生成安全的测试数据
 */
export function generateSafeTestData(count: number = 30, timeFrame: string = '1day'): ChartDataPoint[] {
  const data: ChartDataPoint[] = []
  const basePrice = 2000
  
  for (let i = 0; i < count; i++) {
    const time = timeFrame === 'time' 
      ? new Date(Date.now() - (count - i) * 60000).toISOString() // 分钟数据
      : new Date(Date.now() - (count - i) * 86400000).toISOString() // 日数据
    
    // 生成更合理的价格数据
    const priceVariation = (Math.random() - 0.5) * 50 // 减少价格波动
    const open = Math.max(0, basePrice + priceVariation + (Math.random() - 0.5) * 10)
    const closeVariation = (Math.random() - 0.5) * 20
    const close = Math.max(0, open + closeVariation)
    
    // 确保 high >= max(open, close) 且 low <= min(open, close)
    const maxPrice = Math.max(open, close)
    const minPrice = Math.min(open, close)
    const high = maxPrice + Math.random() * 15
    const low = Math.max(0, minPrice - Math.random() * 15)
    
    data.push({
      time,
      open: Number(open.toFixed(2)),
      high: Number(high.toFixed(2)),
      low: Number(low.toFixed(2)),
      close: Number(close.toFixed(2)),
      volume: Math.floor(Math.random() * 100000),
    })
  }
  
  return data
}

/**
 * 调试图表点击事件
 */
export function debugChartTouch(index: any, chartData: any[]) {
  console.group('🔍 Chart Touch Debug')
  
  console.log('Touch index received:', index)
  console.log('Index type:', typeof index)
  console.log('Chart data length:', chartData?.length || 'undefined')
  
  if (typeof index === 'object' && index !== null) {
    console.log('Index object properties:', Object.keys(index))
    console.log('Index object values:', index)
  }
  
  let actualIndex: number | null = null
  
  if (typeof index === 'number') {
    actualIndex = index
  } else if (typeof index === 'object' && index !== null) {
    actualIndex = index.index || index.dataIndex || index.seriesIndex || null
  }
  
  console.log('Resolved index:', actualIndex)
  
  if (actualIndex !== null && chartData && Array.isArray(chartData)) {
    if (actualIndex >= 0 && actualIndex < chartData.length) {
      const dataPoint = chartData[actualIndex]
      console.log('Data point at index:', dataPoint)
      
      if (dataPoint) {
        console.log('Data point validation:')
        console.log('- Has time:', !!dataPoint.time)
        console.log('- Has open:', typeof dataPoint.open === 'number')
        console.log('- Has high:', typeof dataPoint.high === 'number')
        console.log('- Has low:', typeof dataPoint.low === 'number')
        console.log('- Has close:', typeof dataPoint.close === 'number')
      }
    } else {
      console.error('Index out of range:', actualIndex, 'Data length:', chartData.length)
    }
  } else {
    console.error('Invalid chart data or index')
  }
  
  console.groupEnd()
  
  return actualIndex
}

/**
 * 监控图表组件的状态
 */
export function monitorChartComponent(componentName: string, props: any) {
  console.group(`📊 Chart Component Monitor: ${componentName}`)
  
  console.log('Props received:')
  console.log('- chartData type:', Array.isArray(props.chartData) ? 'array' : typeof props.chartData)
  console.log('- chartData length:', props.chartData?.length || 'undefined')
  console.log('- timeFrame:', props.timeFrame)
  console.log('- loading:', props.loading)
  
  if (Array.isArray(props.chartData) && props.chartData.length > 0) {
    const validation = validateChartData(props.chartData)
    console.log('Data validation result:', validation)
    
    if (!validation.isValid) {
      console.error('❌ Chart data validation failed:', validation.errors)
    }
    
    if (validation.warnings.length > 0) {
      console.warn('⚠️ Chart data warnings:', validation.warnings)
    }
  }
  
  console.groupEnd()
}

/**
 * 创建错误边界处理函数
 */
export function createChartErrorHandler(componentName: string) {
  return function handleChartError(error: Error, context: string) {
    console.group(`🚨 Chart Error in ${componentName}`)
    console.error('Error:', error.message)
    console.error('Stack:', error.stack)
    console.error('Context:', context)
    console.groupEnd()
    
    // 可以在这里添加错误上报逻辑
    // reportError(error, { component: componentName, context })
  }
}

/**
 * 格式化调试输出
 */
export function formatDebugOutput(data: any, title: string = 'Debug Data') {
  console.group(`🔧 ${title}`)
  
  if (typeof data === 'object' && data !== null) {
    Object.entries(data).forEach(([key, value]) => {
      console.log(`${key}:`, value)
    })
  } else {
    console.log('Value:', data)
    console.log('Type:', typeof data)
  }
  
  console.groupEnd()
}
