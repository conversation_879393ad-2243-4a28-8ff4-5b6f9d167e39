<route lang="json5">
{
  style: {
    navigationBarTitleText: '手机端图表测试',
    navigationBarBackgroundColor: '#F4A261',
    navigationBarTextStyle: 'white'
  },
}
</route>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import GoldChart from '@/components/GoldChart.vue'
import { isMobileDevice, getScreenWidth } from '@/utils/mobileChartConfig'

// 测试数据
const chartData = ref([])
const activeTab = ref('1day')
const deviceInfo = ref({
  isMobile: false,
  screenWidth: 0,
  userAgent: '',
})

// 时间选项卡
const timeTabs = [
  { label: '分时', value: 'time' },
  { label: '日K', value: '1day' },
  { label: '周K', value: '1week' },
  { label: '月K', value: '1month' },
]

// 生成测试数据
function generateTestData(timeFrame: string) {
  const data = []
  const basePrice = 2000
  const dataCount = timeFrame === 'time' ? 50 : 30
  
  for (let i = 0; i < dataCount; i++) {
    const time = timeFrame === 'time' 
      ? new Date(Date.now() - (dataCount - i) * 60000).toISOString() // 分钟数据
      : new Date(Date.now() - (dataCount - i) * 86400000).toISOString() // 日数据
    
    const variation = (Math.random() - 0.5) * 100
    const open = basePrice + variation + (Math.random() - 0.5) * 20
    const close = open + (Math.random() - 0.5) * 50
    const high = Math.max(open, close) + Math.random() * 30
    const low = Math.min(open, close) - Math.random() * 30
    
    data.push({
      time,
      open: Number(open.toFixed(2)),
      high: Number(high.toFixed(2)),
      low: Number(low.toFixed(2)),
      close: Number(close.toFixed(2)),
      volume: Math.floor(Math.random() * 100000),
    })
  }
  
  return data
}

// 切换时间框架
function switchTimeFrame(timeFrame: string) {
  activeTab.value = timeFrame
  chartData.value = generateTestData(timeFrame)
}

// 获取设备信息
function getDeviceInfo() {
  deviceInfo.value = {
    isMobile: isMobileDevice(),
    screenWidth: getScreenWidth(),
    // #ifdef H5
    userAgent: navigator.userAgent,
    // #endif
    // #ifndef H5
    userAgent: 'UniApp Environment',
    // #endif
  }
}

onMounted(() => {
  getDeviceInfo()
  chartData.value = generateTestData(activeTab.value)
})
</script>

<template>
  <view class="mobile-chart-test">
    <!-- 设备信息 -->
    <view class="device-info">
      <view class="info-title">设备信息</view>
      <view class="info-item">
        <text class="info-label">移动设备:</text>
        <text class="info-value">{{ deviceInfo.isMobile ? '是' : '否' }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">屏幕宽度:</text>
        <text class="info-value">{{ deviceInfo.screenWidth }}px</text>
      </view>
      <view class="info-item">
        <text class="info-label">用户代理:</text>
        <text class="info-value small">{{ deviceInfo.userAgent }}</text>
      </view>
    </view>

    <!-- 图表测试区域 -->
    <view class="chart-test-section">
      <view class="section-title">手机端图表交互测试</view>
      
      <!-- 时间框架切换 -->
      <view class="time-tabs">
        <text
          v-for="tab in timeTabs" 
          :key="tab.value"
          class="tab-item" 
          :class="{ active: activeTab === tab.value }"
          @click="switchTimeFrame(tab.value)"
        >
          {{ tab.label }}
        </text>
      </view>

      <!-- 图表组件 -->
      <view class="chart-container">
        <GoldChart 
          :chart-data="chartData" 
          :time-frame="activeTab" 
          :loading="false" 
        />
      </view>
    </view>

    <!-- 交互说明 -->
    <view class="interaction-guide">
      <view class="guide-title">交互说明</view>
      <view class="guide-item">
        <text class="guide-icon">👆</text>
        <text class="guide-text">点击图表查看具体数据点</text>
      </view>
      <view class="guide-item">
        <text class="guide-icon">👈👉</text>
        <text class="guide-text">左右滑动浏览更多数据</text>
      </view>
      <view class="guide-item">
        <text class="guide-icon">📱</text>
        <text class="guide-text">针对手机屏幕优化显示</text>
      </view>
      <view class="guide-item">
        <text class="guide-icon">🎯</text>
        <text class="guide-text">触摸反馈和防误触优化</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.mobile-chart-test {
  padding: 20rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(244, 162, 97, 0.05) 100%);
  min-height: 100vh;
}

.device-info {
  background: rgba(244, 162, 97, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid rgba(244, 162, 97, 0.2);

  .info-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 16rpx;
  }

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;

    .info-label {
      font-size: 24rpx;
      color: #A0522D;
      font-weight: 500;
    }

    .info-value {
      font-size: 24rpx;
      color: #8B4513;
      font-weight: 600;

      &.small {
        font-size: 20rpx;
        max-width: 400rpx;
        text-align: right;
        word-break: break-all;
      }
    }
  }
}

.chart-test-section {
  margin-bottom: 30rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 20rpx;
    text-align: center;
  }
}

.time-tabs {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
  padding: 0 20rpx;

  .tab-item {
    padding: 16rpx 32rpx;
    background: rgba(244, 162, 97, 0.1);
    border-radius: 25rpx;
    font-size: 24rpx;
    color: #A0522D;
    border: 2rpx solid rgba(244, 162, 97, 0.3);
    transition: all 0.3s ease;

    &.active {
      background: linear-gradient(135deg, #F4A261 0%, #E9C46A 100%);
      color: white;
      border-color: #F4A261;
      box-shadow: 0 4rpx 12rpx rgba(244, 162, 97, 0.3);
    }
  }
}

.chart-container {
  margin-bottom: 30rpx;
}

.interaction-guide {
  background: rgba(233, 196, 106, 0.1);
  border-radius: 12rpx;
  padding: 24rpx;
  border: 2rpx solid rgba(233, 196, 106, 0.2);

  .guide-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #8B4513;
    margin-bottom: 20rpx;
    text-align: center;
  }

  .guide-item {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .guide-icon {
      font-size: 32rpx;
      margin-right: 16rpx;
      width: 50rpx;
      text-align: center;
    }

    .guide-text {
      font-size: 24rpx;
      color: #A0522D;
      flex: 1;
    }
  }
}

/* 手机端响应式优化 */
@media screen and (max-width: 750px) {
  .mobile-chart-test {
    padding: 16rpx;
  }

  .time-tabs {
    gap: 12rpx;
    
    .tab-item {
      padding: 12rpx 24rpx;
      font-size: 22rpx;
    }
  }

  .device-info .info-item .info-value.small {
    max-width: 300rpx;
    font-size: 18rpx;
  }
}
</style>
