import { http } from '@/utils/http'

// 金价数据类型定义（根据实际API返回数据结构）
export interface GoldPriceData {
  type: string // 金价类型，如"伦敦金（现货黄金）"
  currentPrice: string // 当前价格
  previousSettlement: string // 前一交易日结算价
  buyPrice: string // 买入价
  sellPrice: string // 卖出价
  high: string // 最高价
  low: string // 最低价
  updateTime: string // 更新时间，如"20:08:00"
  unknown1: string // 未知字段1
  open: string // 开盘价
  unknown2: string // 未知字段2
  unknown3: string // 未知字段3
  unknown4: string // 未知字段4
  date: string // 日期，如"2025-06-25"
  name: string // 名称
  timestamp: number // 时间戳
  createTime: string // 创建时间，如"2025-06-25 20:08:00"
}

// 图表数据类型定义
export interface ChartDataPoint {
  time: string
  open: number
  high: number
  low: number
  close: number
  volume?: number
}

// K线数据类型定义
export interface KLineData {
  _id: string
  date_time: string
  buy: number
  diffper: number
  high: number
  low: number
  new: number
  open: number
  sell: number
  variety: string
  volume?: number
}

// 分时数据类型定义
export interface TimeData {
  time: string
  price: number
  volume?: number
}

// 金价API类
export class GoldAPI {
  /**
   * 获取实时金价数据
   */
  async getGoldPrices(): Promise<GoldPriceData[]> {
    const response = await http.get<GoldPriceData[]>(
      '/api/open/gold/openGoldPricesController/getNewPrice/',
    )
    return response.data
  }

  /**
   * 获取日K线数据
   */
  async getDaysGoldData(): Promise<KLineData[]> {
    const response = await http.get<KLineData[]>(
      '/api/open/gold/openDaysGoldController/',
    )
    return response.data
  }

  /**
   * 获取分时数据
   */
  async getTimeGoldData(): Promise<TimeData[]> {
    const response = await http.get<TimeData[]>(
      '/api/open/gold/openTimeGoldController/',
    )
    return response.data
  }

  /**
   * 获取历史数据
   * @param startDate 开始日期
   * @param endDate 结束日期
   */
  async getHistoryData(startDate: string, endDate: string): Promise<KLineData[]> {
    const response = await http.get<KLineData[]>(
      '/api/open/gold/openHistoryGoldController/',
      {
        start_date: startDate,
        end_date: endDate,
      },
    )
    return response.data
  }

  /**
   * 获取技术指标数据
   * @param indicator 指标类型 (MA, MACD, RSI等)
   * @param period 周期
   */
  async getTechnicalIndicators(indicator: string, period: number = 14): Promise<any> {
    const response = await http.get<any>(
      '/api/open/gold/openTechnicalController/',
      {
        indicator,
        period,
      },
    )
    return response
  }

  /**
   * 获取市场新闻
   */
  async getMarketNews(): Promise<any[]> {
    const response = await http.get<any[]>(
      '/api/open/gold/openNewsController/',
    )
    return response.data
  }

  /**
   * 获取AI分析报告
   * @param timeframe 时间框架
   */
  async getAIAnalysis(timeframe: string = '1day'): Promise<any> {
    const response = await http.get<any>(
      '/api/open/gold/openAIAnalysisController/',
      {
        timeframe,
      },
    )
    return response
  }

  /**
   * 数据处理工具方法
   */

  /**
   * 格式化价格显示
   * @param price 价格
   * @param currency 货币符号
   */
  formatPrice(price: string | number, currency: string = '¥'): string {
    const numPrice = typeof price === 'string' ? Number.parseFloat(price) : price
    if (Number.isNaN(numPrice))
      return '--'

    return `${currency}${numPrice.toFixed(2)}`
  }

  /**
   * 计算涨跌幅百分比
   * @param change 变化值
   * @param basePrice 基准价格
   */
  formatChangePercent(change: string | number, basePrice: string | number): string {
    const numChange = typeof change === 'string' ? Number.parseFloat(change) : change
    const numBase = typeof basePrice === 'string' ? Number.parseFloat(basePrice) : basePrice

    if (Number.isNaN(numChange) || Number.isNaN(numBase) || numBase === 0)
      return '0.00%'

    const percent = (numChange / numBase) * 100
    const sign = percent > 0 ? '+' : ''
    return `${sign}${percent.toFixed(2)}%`
  }

  /**
   * 格式化时间显示
   * @param dateTime 时间字符串
   */
  formatDateTime(dateTime: string): string {
    try {
      const date = new Date(dateTime)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}`
    }
    catch (error) {
      return dateTime
    }
  }

  /**
   * 判断价格变化趋势
   * @param change 变化值
   */
  getPriceTrend(change: number): 'up' | 'down' | 'neutral' {
    if (change > 0)
      return 'up'
    if (change < 0)
      return 'down'
    return 'neutral'
  }

  /**
   * 计算移动平均线
   * @param data 价格数据
   * @param period 周期
   */
  calculateMA(data: number[], period: number): number[] {
    const result: number[] = []

    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        result.push(Number.NaN)
      }
      else {
        const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)
        result.push(sum / period)
      }
    }

    return result
  }

  /**
   * 计算RSI指标
   * @param prices 价格数组
   * @param period 周期，默认14
   */
  calculateRSI(prices: number[], period: number = 14): number[] {
    const rsi: number[] = []
    const gains: number[] = []
    const losses: number[] = []

    // 计算价格变化
    for (let i = 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1]
      gains.push(change > 0 ? change : 0)
      losses.push(change < 0 ? Math.abs(change) : 0)
    }

    // 计算RSI
    for (let i = period - 1; i < gains.length; i++) {
      const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period
      const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period

      if (avgLoss === 0) {
        rsi.push(100)
      }
      else {
        const rs = avgGain / avgLoss
        rsi.push(100 - (100 / (1 + rs)))
      }
    }

    return rsi
  }
}

// 导出API实例
export const goldApi = new GoldAPI()

// 导出默认实例
export default goldApi
