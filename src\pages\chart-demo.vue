<script>
import BeautifulKLineChart from '@/components/BeautifulKLineChart.vue'

export default {
  components: {
    BeautifulKLineChart,
  },
  data() {
    return {}
  },
}
</script>

<template>
  <view class="chart-demo-page">
    <view class="header">
      <text class="title">
        美化K线图演示
      </text>
      <text class="subtitle">
        金色主题 + 中国股市颜色传统
      </text>
    </view>

    <view class="chart-section">
      <view class="section-title">
        <text>官方默认样式对比</text>
      </view>

      <!-- 美化后的K线图 -->
      <view class="chart-container">
        <view class="chart-header">
          <text class="chart-title">
            美化版K线图
          </text>
          <text class="chart-desc">
            金色主题 · 红涨绿跌 · 移动平均线
          </text>
        </view>
        <BeautifulKLineChart />
      </view>

      <!-- 配置说明 -->
      <view class="config-info">
        <view class="info-title">
          <text>美化配置特点</text>
        </view>
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">
              🎨 颜色主题：
            </text>
            <text class="info-value">
              金色系配色方案，温暖优雅
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">
              📈 涨跌颜色：
            </text>
            <text class="info-value">
              红涨绿跌（中国股市传统）
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">
              📊 移动平均线：
            </text>
            <text class="info-value">
              MA5(金色) MA10(暗金) MA20(秘鲁色)
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">
              🎯 网格线：
            </text>
            <text class="info-value">
              虚线样式，金色半透明
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">
              💡 提示框：
            </text>
            <text class="info-value">
              圆角白色背景，金色边框
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">
              🔄 滚动条：
            </text>
            <text class="info-value">
              金色滚动条，右对齐显示
            </text>
          </view>
        </view>
      </view>

      <!-- 颜色说明 -->
      <view class="color-palette">
        <view class="palette-title">
          <text>配色方案</text>
        </view>
        <view class="color-grid">
          <view class="color-item">
            <view class="color-box" style="background: #DC143C;" />
            <text class="color-name">
              上涨红
            </text>
            <text class="color-code">
              #DC143C
            </text>
          </view>
          <view class="color-item">
            <view class="color-box" style="background: #228B22;" />
            <text class="color-name">
              下跌绿
            </text>
            <text class="color-code">
              #228B22
            </text>
          </view>
          <view class="color-item">
            <view class="color-box" style="background: #D4AF37;" />
            <text class="color-name">
              主金色
            </text>
            <text class="color-code">
              #D4AF37
            </text>
          </view>
          <view class="color-item">
            <view class="color-box" style="background: #B8860B;" />
            <text class="color-name">
              暗金色
            </text>
            <text class="color-code">
              #B8860B
            </text>
          </view>
          <view class="color-item">
            <view class="color-box" style="background: #CD853F;" />
            <text class="color-name">
              秘鲁色
            </text>
            <text class="color-code">
              #CD853F
            </text>
          </view>
          <view class="color-item">
            <view class="color-box" style="background: #8B4513;" />
            <text class="color-name">
              文字色
            </text>
            <text class="color-code">
              #8B4513
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.chart-demo-page {
  padding: 20rpx;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(244, 162, 97, 0.05) 50%,
    rgba(233, 196, 106, 0.08) 100%
  );
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 20rpx;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 18rpx;
  border: 2rpx solid rgba(244, 162, 97, 0.2);
  backdrop-filter: blur(10px);

  .title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #8b4513;
    margin-bottom: 12rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #cd853f;
    font-weight: 500;
  }
}

.chart-section {
  .section-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #8b4513;
    margin-bottom: 20rpx;
    text-align: center;
  }
}

.chart-container {
  margin-bottom: 40rpx;

  .chart-header {
    padding: 20rpx;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(244, 162, 97, 0.08) 100%);
    border-radius: 12rpx 12rpx 0 0;
    border: 2rpx solid rgba(244, 162, 97, 0.2);
    border-bottom: none;

    .chart-title {
      display: block;
      font-size: 26rpx;
      font-weight: bold;
      color: #8b4513;
      margin-bottom: 8rpx;
    }

    .chart-desc {
      font-size: 20rpx;
      color: #cd853f;
    }
  }
}

.config-info {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 18rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  border: 2rpx solid rgba(244, 162, 97, 0.2);
  backdrop-filter: blur(10px);

  .info-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #8b4513;
    margin-bottom: 24rpx;
    text-align: center;
  }

  .info-list {
    .info-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16rpx;
      padding: 12rpx;
      background: rgba(244, 162, 97, 0.05);
      border-radius: 8rpx;

      .info-label {
        font-size: 22rpx;
        color: #8b4513;
        font-weight: 600;
        min-width: 140rpx;
      }

      .info-value {
        font-size: 22rpx;
        color: #cd853f;
        flex: 1;
      }
    }
  }
}

.color-palette {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 18rpx;
  padding: 30rpx;
  border: 2rpx solid rgba(244, 162, 97, 0.2);
  backdrop-filter: blur(10px);

  .palette-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #8b4513;
    margin-bottom: 24rpx;
    text-align: center;
  }

  .color-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;

    .color-item {
      text-align: center;
      padding: 16rpx;
      background: rgba(244, 162, 97, 0.05);
      border-radius: 12rpx;

      .color-box {
        width: 60rpx;
        height: 60rpx;
        border-radius: 8rpx;
        margin: 0 auto 12rpx;
        border: 2rpx solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      }

      .color-name {
        display: block;
        font-size: 20rpx;
        color: #8b4513;
        font-weight: 600;
        margin-bottom: 4rpx;
      }

      .color-code {
        font-size: 18rpx;
        color: #cd853f;
        font-family: monospace;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 750px) {
  .color-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}
</style>
